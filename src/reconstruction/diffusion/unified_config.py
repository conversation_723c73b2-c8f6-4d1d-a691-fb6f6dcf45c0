#!/usr/bin/env python3
"""
Unified Configuration System for Diffusion Model Pipeline

This module provides a centralized configuration system that replaces
the scattered configuration logic across preprocess_diffusion_data.py,
train_diffusion.py, and unfold_diffusion.py.
"""

import os
import yaml
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, List

# Optional torch import for preprocessing-only usage
try:
    import torch

    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None


class UnifiedDiffusionConfig:
    """
    Unified configuration class for the entire diffusion model pipeline.

    This class loads configuration from a YAML file and provides a consistent
    interface for all pipeline components.
    """

    def __init__(self, config_path: str = "diffusion_config.yaml"):
        """
        Initialize configuration from YAML file.

        Args:
            config_path: Path to the configuration YAML file
        """
        self.config_path = config_path
        self._load_config()
        self._validate_config()
        self._compute_derived_values()

    def _load_config(self):
        """Load configuration from YAML file."""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")

        with open(self.config_path, "r") as f:
            self.config = yaml.safe_load(f)

        # Extract main sections for easier access
        self.data = self.config.get("data", {})
        self.data_processing = self.config.get("data_processing", {})
        self.model = self.config.get("model", {})
        self.training = self.config.get("training", {})
        self.unfolding = self.config.get("unfolding", {})
        self.system = self.config.get("system", {})
        self.evaluation = self.config.get("evaluation", {})
        self.compatibility = self.config.get("compatibility", {})

    def _validate_config(self):
        """Validate configuration parameters."""
        required_sections = ["data", "data_processing", "model", "training", "system"]
        for section in required_sections:
            if section not in self.config:
                raise ValueError(
                    f"Required configuration section '{section}' not found"
                )

        # Validate critical parameters
        if self.training.get("batch_size", 0) <= 0:
            raise ValueError("batch_size must be positive")

        if self.training.get("epochs", 0) <= 0:
            raise ValueError("epochs must be positive")

        if self.data_processing.get("pt_conditioning_moments", 0) <= 0:
            raise ValueError("pt_conditioning_moments must be positive")

    def _compute_derived_values(self):
        """Compute derived configuration values."""
        # Calculate input dimension if not specified
        if self.model.get("input_dim") is None:
            detector_4vec_dim = 12  # 3 particles × 4 components each
            conditioning_dim = (
                2 * self.data_processing["pt_conditioning_moments"]  # 2 leptons
                + 2 * self.data_processing["eta_conditioning_moments"]  # eta moments
                + 2 * self.data_processing["phi_conditioning_moments"]  # phi moments
            )
            self.model["input_dim"] = detector_4vec_dim + conditioning_dim

        # Set up device
        self.device = self._get_device()

        # Create state name for checkpoints
        self.state_name = f"{self.training['train_type']}_b{self.training['batch_size']}_it{self.training['epochs']}"

        # Set up normalization vector for target data (neutrino four-vectors)
        self.norm_vec = np.array(
            [
                self.data_processing["E_range"],
                self.data_processing["pT_range"],
                self.data_processing["pT_range"],
                self.data_processing["pT_range"],  # neutrino 1
                self.data_processing["E_range"],
                self.data_processing["pT_range"],
                self.data_processing["pT_range"],
                self.data_processing["pT_range"],  # neutrino 2
            ]
        )

        # Set up paths
        self._setup_paths()

    def _get_device(self):
        """Determine the appropriate device (CUDA/CPU)."""
        requested_device = self.system.get("device", "cuda")
        if not TORCH_AVAILABLE:
            return requested_device  # Return string when torch not available
        if "cuda" in requested_device.lower() and torch.cuda.is_available():
            return torch.device("cuda:0")
        return torch.device("cpu")

    def _setup_paths(self):
        """Set up output paths."""
        # Ensure paths end with /
        for path_key in ["output_path", "plots_path", "ckpt_path"]:
            path = self.system.get(path_key, "")
            if path and not path.endswith("/"):
                self.system[path_key] = path + "/"

    # =============================================================================
    # PROPERTY ACCESSORS FOR BACKWARD COMPATIBILITY
    # =============================================================================

    @property
    def seed(self):
        return self.system.get("seed", 42)

    @property
    def random_seed(self):
        return self.system.get("random_seed", False)

    @property
    def batch_size(self):
        return int(self.training["batch_size"])

    @property
    def epochs(self):
        return int(self.training["epochs"])

    @property
    def lr(self):
        return float(self.training["learning_rate"])

    @property
    def beta_1(self):
        return float(self.model["beta_1"])

    @property
    def beta_T(self):
        return float(self.model["beta_T"])

    @property
    def T(self):
        return int(self.model["T"])

    @property
    def input_dim(self):
        return int(self.model["input_dim"])

    @property
    def output_dim(self):
        return int(self.model["output_dim"])

    @property
    def hidden_dim(self):
        return int(self.model["hidden_dim"])

    @property
    def num_layers(self):
        return int(self.model["num_layers"])

    @property
    def time_dim(self):
        return int(self.model["time_dim"])

    @property
    def pt_conditioning_moments(self):
        return int(self.data_processing["pt_conditioning_moments"])

    @property
    def eta_conditioning_moments(self):
        return int(self.data_processing["eta_conditioning_moments"])

    @property
    def phi_conditioning_moments(self):
        return int(self.data_processing["phi_conditioning_moments"])

    @property
    def pT_range(self):
        return float(self.data_processing["pT_range"])

    @property
    def E_range(self):
        return float(self.data_processing["E_range"])

    @property
    def eta_range(self):
        return float(self.data_processing["eta_range"])

    @property
    def phi_range(self):
        return float(self.data_processing["phi_range"])

    @property
    def output_path(self):
        return self.system["output_path"]

    @property
    def plots_path(self):
        return self.system["plots_path"]

    @property
    def ckpt_path(self):
        return self.system["ckpt_path"]

    @property
    def save_int(self):
        return int(self.training.get("save_interval", 100))

    @property
    def train_type(self):
        return str(self.training["train_type"])

    @property
    def unf_type(self):
        return str(self.unfolding.get("unf_type", "lepqua_CT14lo"))

    @property
    def unfold_size(self):
        return int(self.unfolding.get("unfold_size", 200000))

    @property
    def sample_size(self):
        return int(self.unfolding.get("sample_size", 10000))

    # =============================================================================
    # UTILITY METHODS
    # =============================================================================

    def set_seed(self, seed: Optional[int] = None):
        """Set random seed for reproducibility."""
        if seed is None:
            seed = self.seed
        if self.random_seed:
            seed = np.random.randint(1000)

        if TORCH_AVAILABLE:
            torch.manual_seed(seed)
        np.random.seed(seed)

    def create_directories(self):
        """Create necessary output directories."""
        os.makedirs(self.output_path, exist_ok=True)
        os.makedirs(self.plots_path, exist_ok=True)
        os.makedirs(self.ckpt_path, exist_ok=True)

    def get_model_config(self) -> Dict[str, Any]:
        """Get configuration dictionary for model initialization."""
        return {
            "device": self.device,
            "beta_1": self.beta_1,
            "beta_T": self.beta_T,
            "T": self.T,
            "input_dim": self.input_dim,
            "output_dim": self.output_dim,
            "hidden_dim": self.hidden_dim,
            "num_layers": self.num_layers,
            "time_dim": self.time_dim,
            "lr": self.lr,
            "batch_size": self.batch_size,
            "epochs": self.epochs,
        }

    def get_data_config(self) -> Dict[str, Any]:
        """Get configuration dictionary for data preprocessing."""
        return {
            "pt_conditioning_moments": self.pt_conditioning_moments,
            "eta_conditioning_moments": self.eta_conditioning_moments,
            "phi_conditioning_moments": self.phi_conditioning_moments,
            "pT_range": self.pT_range,
            "E_range": self.E_range,
            "eta_range": self.eta_range,
            "phi_range": self.phi_range,
            "max_zero_columns": self.data_processing.get("max_zero_columns", 2),
            "random_state": self.data_processing.get("random_state", 42),
            "norm_vec": self.norm_vec,
        }

    def get_file_paths(self) -> Dict[str, Any]:
        """Get all file paths from configuration."""
        return {
            "detector_files": self.data.get("preprocessing", {}).get(
                "detector_files", []
            ),
            "truth_files": self.data.get("preprocessing", {}).get("truth_files", []),
            "detector_dataset_path": self.data.get("detector_dataset_path"),
            "truth_dataset_path": self.data.get("truth_dataset_path"),
            "detector_sim_path": self.data.get("detector_sim_path"),
            "truth_path": self.data.get("truth_path"),
            "data_path": self.data.get("data_path"),
            "raw_data_path": self.data.get("raw_data_path"),
        }

    def print_summary(self):
        """Print a summary of the configuration."""
        print("=" * 60)
        print("UNIFIED DIFFUSION CONFIGURATION SUMMARY")
        print("=" * 60)
        print(f"Configuration file: {self.config_path}")
        print(f"Device: {self.device}")
        print(f"State name: {self.state_name}")
        print()
        print("Model Architecture:")
        print(f"  Input dim: {self.input_dim}")
        print(f"  Output dim: {self.output_dim}")
        print(f"  Hidden dim: {self.hidden_dim}")
        print(f"  Layers: {self.num_layers}")
        print()
        print("Training Parameters:")
        print(f"  Epochs: {self.epochs}")
        print(f"  Batch size: {self.batch_size}")
        print(f"  Learning rate: {self.lr}")
        print()
        print("Data Processing:")
        print(f"  pT moments: {self.pt_conditioning_moments}")
        print(f"  Eta moments: {self.eta_conditioning_moments}")
        print(f"  Phi moments: {self.phi_conditioning_moments}")
        print("=" * 60)


def load_unified_config(
    config_path: str = "diffusion_config.yaml",
) -> UnifiedDiffusionConfig:
    """
    Convenience function to load unified configuration.

    Args:
        config_path: Path to configuration file

    Returns:
        UnifiedDiffusionConfig instance
    """
    return UnifiedDiffusionConfig(config_path)
