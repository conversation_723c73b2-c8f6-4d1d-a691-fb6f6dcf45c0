from src.evaluation.evaluation import calculate_results, calculate_results_diff_analysis
import numpy as np
import pandas as pd

data_neutrino = pd.read_csv("outputs/diffusion_unified/unfold_diffusion_lepqua_CT14lo.csv")
data_leptons = pd.read_csv("data/hww_sherpa_1M_MG_final_detector_sim_cuts.csv")

data = pd.concat([data_leptons, data_neutrino], axis=1)

X = data[
    [
        "p_l_1_E",
        "p_l_1_x",
        "p_l_1_y",
        "p_l_1_z",
        "p_l_2_E",
        "p_l_2_x",
        "p_l_2_y",
        "p_l_2_z",
    ]
]

y = data[
    [
        "p_v_1_E_diffusion",
        "p_v_1_x_diffusion",
        "p_v_1_y_diffusion",
        "p_v_1_z_diffusion",
        "p_v_2_E_diffusion",
        "p_v_2_x_diffusion",
        "p_v_2_y_diffusion",
        "p_v_2_z_diffusion",
    ]
]

types = data["Event.Type"]

final = np.concatenate((X, y), axis=1)

result = calculate_results(
    arrays=[final],
    labels=["Diffusion"],
    title="HWW",
    types=[types],
)

result.run("plots")