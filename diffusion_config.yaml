# Unified Configuration for Diffusion Model Pipeline
# This file serves as the single source of truth for all diffusion model scripts:
# - preprocess_diffusion_data.py
# - train_diffusion.py  
# - unfold_diffusion.py

# =============================================================================
# DATA PATHS AND FILES
# =============================================================================

data:
  # Input data paths
  data_path: "data/hww_1M_MG_final"
  raw_data_path: "data/hww_1M_MG_no_cuts"
  processed_features_path: "data/hww_1M_MG_final_X"
  processed_targets_path: "data/hww_1M_MG_final_y"
  detector_sim_path: "data/hww_sherpa_1M_MG_final_detector_sim"
  truth_path: "data/hww_1M_MG_final_truth"
  
  # Coordinated preprocessed datasets (generated by preprocess_diffusion_data.py)
  detector_dataset_path: "diffusion_training_dataset.csv"
  truth_dataset_path: "diffusion_training_targets.csv"
  
  # Input file pairs for preprocessing
  preprocessing:
    detector_files:
      - "data/hww_1M_MG_final_detector_sim_cuts.csv"
      # - "data/ww_1M_MG_final_detector_sim_cuts.csv"
      # - "data/ttbar_1M_MG_final_detector_sim_cuts.csv"
    truth_files:
      - "data/hww_1M_MG_final_truth_cuts.csv"
      # - "data/ww_1M_MG_final_truth_cuts.csv"
      # - "data/ttbar_1M_MG_final_truth_cuts.csv"

# =============================================================================
# DATA PROCESSING CONFIGURATION
# =============================================================================

data_processing:
  # Filtering parameters
  max_zero_columns: 2  # Remove rows with more than this many zero values
  
  # Conditioning feature parameters
  pt_conditioning_moments: 0
  eta_conditioning_moments: 0
  phi_conditioning_moments: 0
  
  # Normalization ranges (in GeV)
  eta_range: 2.5
  phi_range: 3.5
  pT_range: 300
  E_range: 400
  
  # Data dimensions
  n_dims: 8  # 4 components for each of 2 neutrinos (E, px, py, pz)
  
  # Shuffling and sampling
  random_state: 42
  max_rows: null  # Set to integer to limit dataset size, null for no limit

# =============================================================================
# MODEL ARCHITECTURE
# =============================================================================

model:
  # Input/output dimensions (calculated automatically if null)
  input_dim: null  # Will be calculated: 12 (detector 4-vecs) + conditioning features
  output_dim: 8    # 2 neutrinos × 4 components each
  
  # Network architecture
  hidden_dim: 256
  num_layers: 4
  time_dim: 32
  
  # Diffusion parameters
  beta_1: 1e-4
  beta_T: 0.02
  T: 500

# =============================================================================
# TRAINING CONFIGURATION
# =============================================================================

training:
  # Basic training parameters
  epochs: 100
  batch_size: 1024
  learning_rate: 3e-4
  
  # Data splitting
  train_split: 0.9
  validation_split: 0.1
  
  # Checkpointing
  save_interval: 100
  save_checkpoints: true
  
  # Training type identifier
  train_type: "diffusion_unified"

# =============================================================================
# UNFOLDING CONFIGURATION
# =============================================================================

unfolding:
  # Unfolding parameters
  unfold_size: 200000  # Maximum number of events to unfold
  sample_size: 10000   # Batch size for unfolding
  
  # Unfolding type identifier
  unf_type: "lepqua_CT14lo"
  
  # Output format
  output_format: "lorentz_vectors"  # Options: "arrays", "lorentz_vectors"

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

system:
  # Random seed
  seed: 42
  random_seed: false
  
  # Device configuration
  device: "cuda"  # Will fallback to CPU if CUDA not available
  
  # Output paths
  output_path: "./outputs/diffusion_case0/"
  plots_path: "./plots/diffusion_case0/"
  ckpt_path: "./model-state/diffusion_case0/"
  
  # Logging
  verbose: true

# =============================================================================
# EVALUATION CONFIGURATION
# =============================================================================

evaluation:
  # Bell test configuration
  enable_bell_tests: true
  
  # Gell-Mann matrix calculation
  enable_gellmann: true
  
  # Mode calculation settings
  mode_calculation:
    method: "kde"  # Options: "histogram", "kde", "gaussian_fit"
    n_samples_for_modes: 100
    
  # Reconstruction quality metrics
  quality_metrics:
    - "mae"
    - "mse" 
    - "rmse"

# =============================================================================
# COMPATIBILITY SETTINGS
# =============================================================================

compatibility:
  # Ensure output format matches existing reconstruction methods
  output_format: "lorentz_vectors"  # Options: "arrays", "lorentz_vectors"
  
  # Integration with existing data preprocessing
  use_existing_preprocessor: true
  
  # Evaluation integration
  integrate_with_evaluation: true
  
  # Legacy support
  load_preprocessed_training_data: true
