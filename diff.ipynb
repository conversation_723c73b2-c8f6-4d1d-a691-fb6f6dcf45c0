import numpy as np
import pandas as pd

npy_path = "outputs/diffusion_unified/unfold_diffusion_lepqua_CT14lo.npy"
csv_path = npy_path.replace(".npy", ".csv")

# Load .npy file
data = np.load(npy_path)

# Wrap into DataFrame (adds column names if you like)
n_cols = data.shape[1]
columns = [f"col_{i}" for i in range(n_cols)]
df = pd.DataFrame(data, columns=columns)
df.drop(columns=["col_0"], inplace=True)

# Rename columns
df.columns = [
    "p_v_1_E_diffusion",
    "p_v_1_x_diffusion",
    "p_v_1_y_diffusion",
    "p_v_1_z_diffusion",
    "p_v_2_E_diffusion",
    "p_v_2_x_diffusion",
    "p_v_2_y_diffusion",
    "p_v_2_z_diffusion",
]

# Save to CSV
df.to_csv(csv_path, index=False)

print(f"Saved: {csv_path}")
df.head()

# Load truth data
truth_data = pd.read_csv("data/hww_sherpa_1M_MG_final_truth_cuts.csv")

# Plot comparison of neutrinos of truth and diffusion
import matplotlib.pyplot as plt

plt.hist(truth_data["p_v_2_z_truth"], bins=50, alpha=0.5, label="Truth", range=(-180, 180), density=True)
plt.hist(df["p_v_2_z_diffusion"], bins=50, alpha=0.5, label="Diffusion", range=(-180, 180), density=True)
plt.legend()
plt.show()

plt.hist(truth_data["p_v_1_E_truth"], bins=50, alpha=0.5, label="Truth", range=(-150, 150), density=True)
plt.hist(df["p_v_1_E_diffusion"], bins=50, alpha=0.5, label="Diffusion", range=(-150, 150), density=True)
plt.legend()
plt.show()

# Load ww and hww and compare
ww_data = pd.read_csv("data/hww_sherpa_1M_MG_final_truth_cuts.csv")[:200000]
hww_data = pd.read_csv("data/hww_1M_MG_final_truth_cuts.csv")[:200000]

# Plot 1d hist of ww/hww
plt.hist(ww_data["p_l_1_E_truth"], bins=50, alpha=0.5, label="Sherpa", range=(-10, 250), density=True)
plt.hist(hww_data["p_l_1_E_truth"], bins=50, alpha=0.5, label="MG", range=(-10, 250), density=True)
plt.legend()
plt.show()

# Calculate transverse momentum for WW and HWW truth
p_T_ww = np.sqrt(ww_data["p_l_1_x_truth"]**2 + ww_data["p_l_1_y_truth"]**2)
p_T_hww = np.sqrt(hww_data["p_l_1_x_truth"]**2 + hww_data["p_l_1_y_truth"]**2)

# Compute eta
p_norm_ww1 = np.sqrt(ww_data["p_l_1_x_truth"]**2 + ww_data["p_l_1_y_truth"]**2 + ww_data["p_l_1_z_truth"]**2)
eta_ww1 = 0.5 * np.log((p_norm_ww1 + ww_data["p_l_1_z_truth"]) / (p_norm_ww1 - ww_data["p_l_1_z_truth"] + 1e-15))
p_norm_ww2 = np.sqrt(ww_data["p_l_2_x_truth"]**2 + ww_data["p_l_2_y_truth"]**2 + ww_data["p_l_2_z_truth"]**2)
eta_ww2 = 0.5 * np.log((p_norm_ww2 + ww_data["p_l_2_z_truth"]) / (p_norm_ww2 - ww_data["p_l_2_z_truth"] + 1e-15))

delta_eta_ww = eta_ww1 - eta_ww2

p_norm_hww1 = np.sqrt(hww_data["p_l_1_x_truth"]**2 + hww_data["p_l_1_y_truth"]**2 + hww_data["p_l_1_z_truth"]**2)
eta_hww1 = 0.5 * np.log((p_norm_hww1 + hww_data["p_l_1_z_truth"]) / (p_norm_hww1 - hww_data["p_l_1_z_truth"] + 1e-15))
p_norm_hww2 = np.sqrt(hww_data["p_l_2_x_truth"]**2 + hww_data["p_l_2_y_truth"]**2 + hww_data["p_l_2_z_truth"]**2)
eta_hww2 = 0.5 * np.log((p_norm_hww2 + hww_data["p_l_2_z_truth"]) / (p_norm_hww2 - hww_data["p_l_2_z_truth"] + 1e-15))
delta_eta_hww = eta_hww1 - eta_hww2

# Plot delta eta comparison
plt.hist(delta_eta_ww, bins=50, alpha=0.5, label="Sherpa", range=(-3, 3), density=True)
plt.hist(delta_eta_hww, bins=50, alpha=0.5, label="MG", range=(-3, 3), density=True)
plt.xlabel("delta eta")
plt.legend()
plt.show()

# Compute phi for WW truth and HWW truth
phi_ww = np.arctan2(ww_data["p_l_1_y_truth"], ww_data["p_l_1_x_truth"])
phi_hww = np.arctan2(hww_data["p_l_1_y_truth"], hww_data["p_l_1_x_truth"])

delta_phi_ww = np.arctan2(ww_data["p_l_1_y_truth"], ww_data["p_l_1_x_truth"]) - np.arctan2(ww_data["p_l_2_y_truth"], ww_data["p_l_2_x_truth"])
delta_phi_hww = np.arctan2(hww_data["p_l_1_y_truth"], hww_data["p_l_1_x_truth"]) - np.arctan2(hww_data["p_l_2_y_truth"], hww_data["p_l_2_x_truth"])
delta_phi_hww = (delta_phi_hww + np.pi) % (2 * np.pi) - np.pi
delta_phi_ww = (delta_phi_ww + np.pi) % (2 * np.pi) - np.pi

# Plot delta phi comparison
plt.hist(delta_phi_ww, bins=50, alpha=0.5, label="Sherpa", range=(-2 * np.pi, 2 * np.pi), density=True)
plt.hist(delta_phi_hww, bins=50, alpha=0.5, label="MG", range=(-2 * np.pi, 2 * np.pi), density=True)
plt.xlabel("delta phi")
plt.legend()
plt.show()


# Plot p_T comparison
plt.hist(p_T_ww, bins=50, alpha=0.5, label="Sherpa", range=(0, 150), density=True)
plt.hist(p_T_hww, bins=50, alpha=0.5, label="MG", range=(0, 150), density=True)
plt.xlabel("p_T lepton 1")
plt.legend()
plt.show()

# Plot eta comparison
plt.hist(eta_ww1, bins=50, alpha=0.5, label="Sherpa", range=(-5, 5), density=True)
plt.hist(eta_hww1, bins=50, alpha=0.5, label="MG", range=(-5, 5), density=True)
plt.xlabel("eta lepton 1")
plt.legend()
plt.show()

# Plot phi comparison
plt.hist(phi_ww, bins=50, alpha=0.5, label="Sherpa", range=(-np.pi, np.pi), density=True)
plt.hist(phi_hww, bins=50, alpha=0.5, label="MG", range=(-np.pi, np.pi), density=True)
plt.xlabel("phi lepton 1")
plt.legend()
plt.show()

met_ww = np.sqrt(ww_data["p_v_1_x_truth"]**2 + ww_data["p_v_1_y_truth"]**2)
met_hww = np.sqrt(hww_data["p_v_1_x_truth"]**2 + hww_data["p_v_1_y_truth"]**2)

# Plot met comparison
plt.hist(met_ww, bins=50, alpha=0.5, label="Sherpa", range=(0, 150), density=True)
plt.hist(met_hww, bins=50, alpha=0.5, label="MG", range=(0, 150), density=True)
plt.xlabel("MET")
plt.legend()
plt.show()